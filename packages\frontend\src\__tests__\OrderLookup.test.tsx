import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { OrderLookup } from '../components/OrderLookup/OrderLookup';
import { apiClient } from '../lib/api-client';

// Mock the API client
vi.mock('../lib/api-client');
const mockApiClient = apiClient as any;

describe('OrderLookup', () => {
  const mockOnOrderFound = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders order lookup form', () => {
    render(<OrderLookup onOrderFound={mockOnOrderFound} />);
    
    expect(screen.getByText('Track Your Order')).toBeInTheDocument();
    expect(screen.getByLabelText('Order Number')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Find My Order' })).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(<OrderLookup onOrderFound={mockOnOrderFound} />);
    
    const submitButton = screen.getByRole('button', { name: 'Find My Order' });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Order number is required')).toBeInTheDocument();
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });

    expect(mockApiClient.findOrder).not.toHaveBeenCalled();
  });

  it('validates email format', async () => {
    render(<OrderLookup onOrderFound={mockOnOrderFound} />);
    
    const orderNumberInput = screen.getByLabelText('Order Number');
    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Find My Order' });

    fireEvent.change(orderNumberInput, { target: { value: '#1001' } });
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
    });

    expect(mockApiClient.findOrder).not.toHaveBeenCalled();
  });

  it('submits form with valid data', async () => {
    const mockOrder = {
      id: 'gid://shopify/Order/123',
      name: '#1001',
      email: '<EMAIL>',
      displayFulfillmentStatus: 'FULFILLED',
      financialStatus: 'PAID',
      fulfillments: { edges: [] },
      lineItems: { edges: [] },
    };

    mockApiClient.findOrder.mockResolvedValue({
      success: true,
      order: mockOrder,
    });

    render(<OrderLookup onOrderFound={mockOnOrderFound} />);
    
    const orderNumberInput = screen.getByLabelText('Order Number');
    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Find My Order' });

    fireEvent.change(orderNumberInput, { target: { value: '#1001' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockApiClient.findOrder).toHaveBeenCalledWith({
        orderNumber: '#1001',
        email: '<EMAIL>',
      });
      expect(mockOnOrderFound).toHaveBeenCalledWith(mockOrder);
    });
  });

  it('displays error when order not found', async () => {
    mockApiClient.findOrder.mockResolvedValue({
      success: false,
      error: 'Order not found. Please check your order number and email and try again.',
    });

    render(<OrderLookup onOrderFound={mockOnOrderFound} />);
    
    const orderNumberInput = screen.getByLabelText('Order Number');
    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Find My Order' });

    fireEvent.change(orderNumberInput, { target: { value: '#1001' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Order not found. Please check your order number and email and try again.')).toBeInTheDocument();
    });

    expect(mockOnOrderFound).not.toHaveBeenCalled();
  });
});
