:root {
  --primary-color: #5a31f4;
  --primary-hover: #4c28d4;
  --text-color: #1a1a1a;
  --background-color: #ffffff;
  --border-color: #e5e5e5;
  --error-color: #ef4444;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  color: var(--text-color);
  background-color: #f5f5f7;
  line-height: 1.5;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.app-container {
  width: 100%;
  max-width: 1200px;
}

@media (max-width: 640px) {
  .app {
    padding: 1rem 0.5rem;
  }
}
