# Product Requirements: Shopify Order Tracking & Returns Widget

## 1. High-Level Objective
To build a secure, self-service, customer-facing widget for our Shopify store. The widget will allow customers to look up their orders using their order number and email, view tracking information, and initiate a self-service return process, including printing a return shipping label.

## 2. Core Components & Architecture
**Frontend**: A client-side widget built with React and TypeScript. It will be responsible for all user interface elements.

**Backend**: A Node.js and Express API service that acts as a secure proxy. It will handle all communication with the Shopify GraphQL Admin API. No Shopify credentials will be stored on the frontend.

## 3. User Stories & Acceptance Criteria

### User Story 1: Order Lookup
As a customer, I want to enter my order number and email address so that I can securely find my order details.

**Acceptance Criteria:**
- The initial screen of the widget must display two input fields: "Order Number" and "Email".
- A "Find My Order" button must be present to trigger the lookup process.
- On button click, the frontend sends the order number and email to the backend API.
- The backend queries the Shopify GraphQL orders endpoint using a combined filter for name and email.
- If no matching order is found, the UI must display a clear error message: "Order not found. Please check your order number and email and try again."
- If a matching order is found, the application must navigate to the Order Details screen.

### User Story 2: Order Tracking
As a customer with a found order, I want to view its status and tracking information so that I know when to expect my delivery.

**Acceptance Criteria:**
- The Order Details screen must display the order number, financial status (e.g., Paid), and fulfillment status (e.g., Shipped, Delivered).
- If the order has tracking information, the UI must display the carrier name, tracking number, and a clickable link to the carrier's tracking page.
- If an order has multiple shipments, all tracking information should be displayed.
- The screen must present two primary action buttons: "Track Order" (which may simply be the display of this information) and "Start a Return".

### User Story 3: Initiate a Return
As a customer, I want to select specific items from my order to return so that I can begin the returns process.

**Acceptance Criteria:**
- Clicking the "Start a Return" button navigates to the Return Selection screen.
- This screen must list all returnable line items from the order, including the product image, title, and quantity ordered.
- Each line item must have a quantity selector allowing the user to choose how many units to return (up to the quantity fulfilled).
- A "Continue" button is enabled only when at least one item is selected for return.
- Clicking "Continue" sends a request to the backend to create the return using the returnCreate mutation.

### User Story 4: Print Return Label
As a customer who has initiated a return, I want to download or print a return shipping label so that I can send my items back.

**Acceptance Criteria:**
- After the return is successfully created, the user is navigated to a confirmation screen.
- This screen must display a message confirming the return has been initiated.
- The screen must feature two buttons: "Download Label (PDF)" and "Print Label".
- The backend must execute the full workflow to generate a shipping label using the Shopify API (creating a reverse delivery).
- Clicking either button should allow the user to access the printable shipping label.
- If a label cannot be generated via the API for any reason, the UI must gracefully fail, providing the user with manual return instructions and a link to the store's returns policy page.
