"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const return_service_js_1 = require("../services/return-service.js");
const validation_js_1 = require("../middleware/validation.js");
const router = (0, express_1.Router)();
const returnService = new return_service_js_1.ReturnService();
// POST /api/returns/create - Create a return
router.post('/create', (0, validation_js_1.validateBody)(validation_js_1.ReturnCreateSchema), async (req, res, next) => {
    try {
        const returnInput = req.body;
        console.log(`Return creation request for order: ${returnInput.orderId}`);
        const result = await returnService.createReturn(returnInput);
        res.json({
            success: true,
            returnId: result.returnId,
            reverseFulfillmentOrderId: result.reverseFulfillmentOrderId,
        });
    }
    catch (error) {
        next(error);
    }
});
// GET /api/returns/:id/label - Generate and retrieve return shipping label
router.get('/:id/label', async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({
                error: 'Return ID is required',
            });
        }
        console.log(`Return label request for return: ${id}`);
        const result = await returnService.generateReturnLabel(id);
        if (result.error) {
            return res.status(400).json({
                success: false,
                error: result.error,
            });
        }
        if (!result.labelUrl) {
            return res.status(404).json({
                success: false,
                error: 'No shipping label available for this return',
            });
        }
        res.json({
            success: true,
            labelUrl: result.labelUrl,
        });
    }
    catch (error) {
        next(error);
    }
});
exports.default = router;
//# sourceMappingURL=returns.js.map