import React, { useState } from 'react';
import { OrderLookupForm } from '../../types/api';
import { apiClient } from '../../lib/api-client';
import styles from './OrderLookup.module.css';

interface OrderLookupProps {
  onOrderFound: (order: any) => void;
}

export const OrderLookup: React.FC<OrderLookupProps> = ({ onOrderFound }) => {
  const [formData, setFormData] = useState<OrderLookupForm>({
    orderNumber: '',
    email: '',
  });
  const [errors, setErrors] = useState<Partial<OrderLookupForm>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string>('');

  const validateForm = (): boolean => {
    const newErrors: Partial<OrderLookupForm> = {};

    if (!formData.orderNumber.trim()) {
      newErrors.orderNumber = 'Order number is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof OrderLookupForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // Clear API error when user makes changes
    if (apiError) {
      setApiError('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setApiError('');

    try {
      const response = await apiClient.findOrder(formData);
      
      if (response.success && response.order) {
        onOrderFound(response.order);
      } else {
        setApiError(response.error || 'Order not found. Please check your order number and email and try again.');
      }
    } catch (error) {
      setApiError('Unable to search for orders at this time. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Track Your Order</h1>
      <p className={styles.subtitle}>
        Enter your order number and email address to view your order details and tracking information.
      </p>

      <form className={styles.form} onSubmit={handleSubmit}>
        <div className={styles.inputGroup}>
          <label htmlFor="orderNumber" className={styles.label}>
            Order Number
          </label>
          <input
            id="orderNumber"
            type="text"
            className={`${styles.input} ${errors.orderNumber ? styles.error : ''}`}
            value={formData.orderNumber}
            onChange={(e) => handleInputChange('orderNumber', e.target.value)}
            placeholder="e.g., #1001"
            disabled={isLoading}
          />
          {errors.orderNumber && (
            <span className={styles.error}>{errors.orderNumber}</span>
          )}
        </div>

        <div className={styles.inputGroup}>
          <label htmlFor="email" className={styles.label}>
            Email Address
          </label>
          <input
            id="email"
            type="email"
            className={`${styles.input} ${errors.email ? styles.error : ''}`}
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="<EMAIL>"
            disabled={isLoading}
          />
          {errors.email && (
            <span className={styles.error}>{errors.email}</span>
          )}
        </div>

        {apiError && (
          <div className={styles.error}>{apiError}</div>
        )}

        <button
          type="submit"
          className={styles.button}
          disabled={isLoading}
        >
          {isLoading ? (
            <span className={styles.loading}>
              <span className={styles.spinner}></span>
              Finding Order...
            </span>
          ) : (
            'Find My Order'
          )}
        </button>
      </form>
    </div>
  );
};
