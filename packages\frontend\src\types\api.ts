// Frontend API Types

export interface Order {
  id: string;
  name: string;
  email: string;
  displayFulfillmentStatus: string;
  financialStatus: string;
  fulfillments: {
    edges: Array<{
      node: {
        id: string;
        trackingInfo: Array<{
          number: string;
          url: string;
          company: string;
        }>;
      };
    }>;
  };
  lineItems: {
    edges: Array<{
      node: {
        id: string;
        title: string;
        quantity: number;
        image?: {
          url: string;
        };
        fulfillmentService: string;
        fulfillableQuantity: number;
      };
    }>;
  };
}

export interface LineItem {
  id: string;
  title: string;
  quantity: number;
  image?: {
    url: string;
  };
  fulfillmentService: string;
  fulfillableQuantity: number;
}

export interface TrackingInfo {
  number: string;
  url: string;
  company: string;
}

export interface ReturnLineItem {
  fulfillmentLineItemId: string;
  quantity: number;
  returnReason: string;
  returnReasonNote?: string;
}

export interface ReturnRequest {
  orderId: string;
  returnLineItems: ReturnLineItem[];
}

// API Response Types
export interface OrderLookupResponse {
  success: boolean;
  order?: Order;
  error?: string;
}

export interface ReturnCreateResponse {
  success: boolean;
  returnId?: string;
  reverseFulfillmentOrderId?: string;
  error?: string;
}

export interface ReturnLabelResponse {
  success: boolean;
  labelUrl?: string;
  error?: string;
}

// Form Types
export interface OrderLookupForm {
  orderNumber: string;
  email: string;
}

export interface ReturnSelection {
  [lineItemId: string]: {
    selected: boolean;
    quantity: number;
    returnReason: string;
    returnReasonNote?: string;
  };
}

// Return Reasons
export const RETURN_REASONS = [
  'Defective',
  'Wrong item',
  'Size too small',
  'Size too large',
  'Not as described',
  'Changed mind',
  'Damaged in shipping',
  'Other',
] as const;

export type ReturnReason = typeof RETURN_REASONS[number];
