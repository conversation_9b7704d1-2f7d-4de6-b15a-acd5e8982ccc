import { Router } from 'express';
import { OrderService } from '../services/order-service.js';
import { validateBody, OrderLookupSchema } from '../middleware/validation.js';

const router = Router();
const orderService = new OrderService();

// POST /api/orders/find - Find order by order number and email
router.post('/find', validateBody(OrderLookupSchema), async (req, res, next) => {
  try {
    const { orderNumber, email } = req.body;
    
    console.log(`Order lookup request: ${orderNumber} for ${email}`);
    
    const result = await orderService.findOrder(orderNumber, email);
    
    if (!result.order) {
      return res.status(404).json({
        error: 'Order not found. Please check your order number and email and try again.',
      });
    }
    
    res.json({
      success: true,
      order: result.order,
    });
  } catch (error) {
    next(error);
  }
});

export default router;
