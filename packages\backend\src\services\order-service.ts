import { createGraphQLClient } from '../lib/shopify-client.js';
import { ShopifyOrder, OrderLookupResponse } from '../types/shopify.js';

const FIND_ORDER_QUERY = `
  query findOrder($query: String!) {
    orders(first: 1, query: $query) {
      edges {
        node {
          id
          name
          email
          displayFulfillmentStatus
          financialStatus
          fulfillments(first: 5) {
            edges {
              node {
                id
                trackingInfo(first: 5) {
                  number
                  url
                  company
                }
              }
            }
          }
          lineItems(first: 20) {
            edges {
              node {
                id
                title
                quantity
                image {
                  url
                }
                fulfillmentService
                fulfillableQuantity
              }
            }
          }
        }
      }
    }
  }
`;

export class OrderService {
  private client = createGraphQLClient();

  async findOrder(orderNumber: string, email: string): Promise<OrderLookupResponse> {
    try {
      // Construct the query string to search by both order name and email
      const query = `name:${orderNumber} AND email:"${email}"`;
      
      console.log(`Searching for order with query: ${query}`);

      const response = await this.client.query({
        data: {
          query: FIND_ORDER_QUERY,
          variables: { query },
        },
      });

      const orders = (response.body as any)?.data?.orders?.edges;
      
      if (!orders || orders.length === 0) {
        console.log('No order found matching the criteria');
        return { order: null };
      }

      const order = orders[0].node as ShopifyOrder;
      console.log(`Found order: ${order.name} for ${order.email}`);

      return { order };
    } catch (error) {
      console.error('Error finding order:', error);
      throw new Error('Failed to find order');
    }
  }
}
