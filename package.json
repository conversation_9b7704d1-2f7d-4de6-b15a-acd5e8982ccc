{"name": "shopify-order-tracking-widget", "version": "1.0.0", "description": "A secure, self-service order tracking and returns widget for Shopify stores", "private": true, "workspaces": ["packages/frontend", "packages/backend"], "scripts": {"dev": "pnpm run --parallel dev", "build": "pnpm run --recursive build", "test": "pnpm run --recursive test", "lint": "pnpm run --recursive lint", "type-check": "pnpm run --recursive type-check"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=20.10.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}