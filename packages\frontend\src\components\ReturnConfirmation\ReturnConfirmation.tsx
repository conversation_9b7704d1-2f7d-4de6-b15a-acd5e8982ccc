import React, { useState, useEffect } from 'react';
import { apiClient } from '../../lib/api-client';
import styles from './ReturnConfirmation.module.css';

interface ReturnConfirmationProps {
  returnId: string;
  orderName: string;
  onBack: () => void;
  onStartOver: () => void;
}

export const ReturnConfirmation: React.FC<ReturnConfirmationProps> = ({ 
  returnId, 
  orderName, 
  onBack, 
  onStartOver 
}) => {
  const [labelUrl, setLabelUrl] = useState<string | null>(null);
  const [isLoadingLabel, setIsLoadingLabel] = useState(false);
  const [labelError, setLabelError] = useState<string>('');
  const [showFallback, setShowFallback] = useState(false);

  const handleDownloadLabel = async () => {
    setIsLoadingLabel(true);
    setLabelError('');
    setShowFallback(false);

    try {
      const response = await apiClient.getReturnLabel(returnId);
      
      if (response.success && response.labelUrl) {
        setLabelUrl(response.labelUrl);
        // Automatically trigger download
        window.open(response.labelUrl, '_blank');
      } else {
        setLabelError(response.error || 'Unable to generate shipping label');
        setShowFallback(true);
      }
    } catch (error) {
      setLabelError('Failed to generate return label. Please try again.');
      setShowFallback(true);
    } finally {
      setIsLoadingLabel(false);
    }
  };

  const handlePrintLabel = async () => {
    if (labelUrl) {
      // Open label in new window for printing
      const printWindow = window.open(labelUrl, '_blank');
      if (printWindow) {
        printWindow.onload = () => {
          printWindow.print();
        };
      }
    } else {
      // Generate label first, then print
      await handleDownloadLabel();
      if (labelUrl) {
        const printWindow = window.open(labelUrl, '_blank');
        if (printWindow) {
          printWindow.onload = () => {
            printWindow.print();
          };
        }
      }
    }
  };

  return (
    <div className={styles.container}>
      <button className={styles.backButton} onClick={onBack}>
        ← Back to Return Selection
      </button>

      <div className={styles.successIcon}>
        ✓
      </div>

      <h1 className={styles.title}>Return Request Created</h1>
      <p className={styles.subtitle}>
        Your return request has been successfully submitted. You can now download and print your return shipping label.
      </p>

      <div className={styles.returnInfo}>
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>Order Number:</span>
          <span className={styles.infoValue}>{orderName}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>Return ID:</span>
          <span className={styles.infoValue}>{returnId}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>Status:</span>
          <span className={styles.infoValue}>Processing</span>
        </div>
      </div>

      {labelError && (
        <div className={styles.error}>
          <div className={styles.errorTitle}>Label Generation Error</div>
          {labelError}
        </div>
      )}

      {showFallback && (
        <div className={styles.fallbackInstructions}>
          <div className={styles.fallbackTitle}>Manual Return Instructions</div>
          <div className={styles.fallbackText}>
            We're unable to generate a shipping label automatically at this time. 
            Please contact our customer service team for manual return instructions 
            and assistance with your return.
          </div>
        </div>
      )}

      <div className={styles.actions}>
        <button 
          className={`${styles.button} ${styles.buttonPrimary}`}
          onClick={handleDownloadLabel}
          disabled={isLoadingLabel}
        >
          {isLoadingLabel ? (
            <span className={styles.loading}>
              <span className={styles.spinner}></span>
              Generating Label...
            </span>
          ) : (
            <>
              📄 Download Label (PDF)
            </>
          )}
        </button>

        <button 
          className={`${styles.button} ${styles.buttonSecondary}`}
          onClick={handlePrintLabel}
          disabled={isLoadingLabel}
        >
          🖨️ Print Label
        </button>

        <button 
          className={`${styles.button} ${styles.buttonSecondary}`}
          onClick={onStartOver}
        >
          Track Another Order
        </button>
      </div>
    </div>
  );
};
