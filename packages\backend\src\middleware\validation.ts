import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

// Validation schemas
export const OrderLookupSchema = z.object({
  orderNumber: z.string().min(1, 'Order number is required'),
  email: z.string().email('Valid email is required'),
});

export const ReturnLineItemSchema = z.object({
  fulfillmentLineItemId: z.string().min(1, 'Fulfillment line item ID is required'),
  quantity: z.number().int().min(1, 'Quantity must be at least 1'),
  returnReason: z.string().min(1, 'Return reason is required'),
  returnReasonNote: z.string().optional(),
});

export const ReturnCreateSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  returnLineItems: z.array(ReturnLineItemSchema).min(1, 'At least one item must be selected for return'),
});

// Validation middleware factory
export const validateBody = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        }));
        
        return res.status(400).json({
          error: 'Validation failed',
          details: errorMessages,
        });
      }
      
      return res.status(400).json({
        error: 'Invalid request data',
      });
    }
  };
};

// Error handling middleware
export const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('API Error:', error);

  // Map specific errors to user-friendly messages
  let statusCode = 500;
  let message = 'An internal server error occurred';

  if (error.message.includes('Order not found') || error.message.includes('No order found')) {
    statusCode = 404;
    message = 'Order not found. Please check your order number and email and try again.';
  } else if (error.message.includes('Return creation failed')) {
    statusCode = 400;
    message = 'Unable to create return. Please ensure the items are eligible for return.';
  } else if (error.message.includes('Failed to find order')) {
    statusCode = 500;
    message = 'Unable to search for orders at this time. Please try again later.';
  } else if (error.message.includes('Failed to create return')) {
    statusCode = 500;
    message = 'Unable to process return request at this time. Please try again later.';
  }

  res.status(statusCode).json({
    error: message,
  });
};
