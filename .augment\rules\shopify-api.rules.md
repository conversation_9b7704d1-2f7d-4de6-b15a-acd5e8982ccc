# Rule: Shopify API Interaction Standards

**Mode**: Always

**Purpose**: To enforce strict, non-negotiable rules for all Shopify API interactions.

## Requirements

1. All API interactions with Shopify MUST use the GraphQL Admin API. The legacy REST Admin API is forbidden.

2. The specific GraphQL Admin API version to target is 2025-07. All queries and mutations must be compatible with this version.

3. All Shopify API requests MUST originate from the backend Node.js service. The frontend client MUST NOT make direct calls to the Shopify API or contain any API credentials.

4. The backend service MUST use the official @shopify/shopify-api library to create and manage the authenticated GraphQL client.

5. All GraphQL IDs must be handled as strings. Do not treat them as numbers.
