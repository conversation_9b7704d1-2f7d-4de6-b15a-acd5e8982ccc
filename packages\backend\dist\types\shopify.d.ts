export interface ShopifyOrder {
    id: string;
    name: string;
    email: string;
    displayFulfillmentStatus: string;
    financialStatus: string;
    fulfillments: {
        edges: Array<{
            node: {
                id: string;
                trackingInfo: Array<{
                    number: string;
                    url: string;
                    company: string;
                }>;
            };
        }>;
    };
    lineItems: {
        edges: Array<{
            node: {
                id: string;
                title: string;
                quantity: number;
                image?: {
                    url: string;
                };
                fulfillmentService: string;
                fulfillableQuantity: number;
            };
        }>;
    };
}
export interface ShopifyReturn {
    id: string;
    name: string;
    status: string;
    reverseFulfillmentOrders: {
        edges: Array<{
            node: {
                id: string;
                status: string;
            };
        }>;
    };
}
export interface ReturnLineItem {
    fulfillmentLineItemId: string;
    quantity: number;
    returnReason: string;
    returnReasonNote?: string;
}
export interface ReturnInput {
    orderId: string;
    returnLineItems: ReturnLineItem[];
}
export interface ReverseDelivery {
    id: string;
    label?: {
        publicFileUrl: string;
    };
}
export interface OrderLookupResponse {
    order: ShopifyOrder | null;
}
export interface ReturnCreateResponse {
    returnId: string;
    reverseFulfillmentOrderId: string;
}
export interface ReturnLabelResponse {
    labelUrl: string | null;
    error?: string;
}
//# sourceMappingURL=shopify.d.ts.map