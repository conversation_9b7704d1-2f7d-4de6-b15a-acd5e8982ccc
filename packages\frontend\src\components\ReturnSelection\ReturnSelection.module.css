.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 1.5rem;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 0.9rem;
}

.backButton {
  background: none;
  border: none;
  color: #5a31f4;
  font-size: 0.875rem;
  cursor: pointer;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.backButton:hover {
  text-decoration: underline;
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.itemCard {
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  padding: 1.5rem;
  transition: border-color 0.2s ease;
}

.itemCard.selected {
  border-color: #5a31f4;
  background: #faf9ff;
}

.itemHeader {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.itemImage {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  background: #f5f5f5;
}

.itemInfo {
  flex: 1;
}

.itemTitle {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.itemQuantity {
  color: #666;
  font-size: 0.875rem;
}

.selectionControls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.checkbox {
  width: 20px;
  height: 20px;
  accent-color: #5a31f4;
}

.quantityControl {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantityLabel {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

.quantityInput {
  width: 60px;
  padding: 0.5rem;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  text-align: center;
}

.reasonSection {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reasonLabel {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

.reasonSelect {
  padding: 0.5rem;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  background: white;
}

.reasonNote {
  padding: 0.5rem;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.actions {
  display: flex;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e5e5;
}

.button {
  flex: 1;
  padding: 0.875rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  border: none;
}

.buttonPrimary {
  background: #5a31f4;
  color: white;
}

.buttonPrimary:hover {
  background: #4c28d4;
}

.buttonPrimary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.buttonSecondary {
  background: #f5f5f5;
  color: #1a1a1a;
  border: 1px solid #e5e5e5;
}

.buttonSecondary:hover {
  background: #e5e5e5;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 1rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
}

@media (max-width: 640px) {
  .container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .itemHeader {
    flex-direction: column;
    text-align: center;
  }
  
  .itemImage {
    align-self: center;
  }
  
  .selectionControls {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .actions {
    flex-direction: column;
  }
}
