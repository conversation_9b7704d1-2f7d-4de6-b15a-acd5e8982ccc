import { createGraphQLClient } from '../lib/shopify-client.js';
import { ReturnInput, ReturnCreateResponse, ReturnLabelResponse, ShopifyReturn, ReverseDelivery } from '../types/shopify.js';

const RETURN_CREATE_MUTATION = `
  mutation returnCreate($returnInput: ReturnInput!) {
    returnCreate(returnInput: $returnInput) {
      return {
        id
        name
        status
        reverseFulfillmentOrders(first: 5) {
          edges {
            node {
              id
              status
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const GET_RETURN_QUERY = `
  query getReturn($id: ID!) {
    return(id: $id) {
      id
      name
      status
      reverseFulfillmentOrders(first: 5) {
        edges {
          node {
            id
            status
          }
        }
      }
    }
  }
`;

const REVERSE_DELIVERY_CREATE_MUTATION = `
  mutation reverseDeliveryCreateWithShipping($reverseFulfillmentOrderId: ID!) {
    reverseDeliveryCreateWithShipping(reverseFulfillmentOrderId: $reverseFulfillmentOrderId) {
      reverseDelivery {
        id
        label {
          publicFileUrl
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export class ReturnService {
  private client = createGraphQLClient();

  async createReturn(returnInput: ReturnInput): Promise<ReturnCreateResponse> {
    try {
      console.log('Creating return with input:', JSON.stringify(returnInput, null, 2));

      const response = await this.client.query({
        data: {
          query: RETURN_CREATE_MUTATION,
          variables: { returnInput },
        },
      });

      const result = response.body?.data?.returnCreate;
      const userErrors = result?.userErrors;

      if (userErrors && userErrors.length > 0) {
        console.error('Return creation errors:', userErrors);
        throw new Error(`Return creation failed: ${userErrors.map((e: any) => e.message).join(', ')}`);
      }

      const returnData = result?.return as ShopifyReturn;
      if (!returnData) {
        throw new Error('No return data received from Shopify');
      }

      const reverseFulfillmentOrderId = returnData.reverseFulfillmentOrders.edges[0]?.node.id;
      if (!reverseFulfillmentOrderId) {
        throw new Error('No reverse fulfillment order created');
      }

      console.log(`Return created successfully: ${returnData.id}`);
      console.log(`Reverse fulfillment order: ${reverseFulfillmentOrderId}`);

      return {
        returnId: returnData.id,
        reverseFulfillmentOrderId,
      };
    } catch (error) {
      console.error('Error creating return:', error);
      throw new Error('Failed to create return');
    }
  }

  async generateReturnLabel(returnId: string): Promise<ReturnLabelResponse> {
    try {
      console.log(`Generating return label for return: ${returnId}`);

      // First, get the return to find the reverse fulfillment order
      const returnResponse = await this.client.query({
        data: {
          query: GET_RETURN_QUERY,
          variables: { id: returnId },
        },
      });

      const returnData = returnResponse.body?.data?.return as ShopifyReturn;
      if (!returnData) {
        throw new Error('Return not found');
      }

      const reverseFulfillmentOrderId = returnData.reverseFulfillmentOrders.edges[0]?.node.id;
      if (!reverseFulfillmentOrderId) {
        throw new Error('No reverse fulfillment order found for this return');
      }

      // Create reverse delivery with shipping label
      const labelResponse = await this.client.query({
        data: {
          query: REVERSE_DELIVERY_CREATE_MUTATION,
          variables: { reverseFulfillmentOrderId },
        },
      });

      const result = labelResponse.body?.data?.reverseDeliveryCreateWithShipping;
      const userErrors = result?.userErrors;

      if (userErrors && userErrors.length > 0) {
        console.error('Label generation errors:', userErrors);
        return {
          labelUrl: null,
          error: `Label generation failed: ${userErrors.map((e: any) => e.message).join(', ')}`,
        };
      }

      const reverseDelivery = result?.reverseDelivery as ReverseDelivery;
      const labelUrl = reverseDelivery?.label?.publicFileUrl;

      if (!labelUrl) {
        return {
          labelUrl: null,
          error: 'No shipping label was generated. Please contact customer service for manual return instructions.',
        };
      }

      console.log(`Return label generated successfully: ${labelUrl}`);

      return { labelUrl };
    } catch (error) {
      console.error('Error generating return label:', error);
      return {
        labelUrl: null,
        error: 'Failed to generate return label. Please contact customer service.',
      };
    }
  }
}
