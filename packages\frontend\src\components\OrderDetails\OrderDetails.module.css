.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 1.5rem;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.orderInfo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.infoLabel {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

.infoValue {
  font-size: 1rem;
  color: #1a1a1a;
  font-weight: 500;
}

.status {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status.shipped {
  background: #dcfce7;
  color: #166534;
}

.status.delivered {
  background: #dbeafe;
  color: #1e40af;
}

.status.pending {
  background: #fef3c7;
  color: #92400e;
}

.section {
  margin-bottom: 2rem;
}

.sectionTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.trackingList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.trackingItem {
  padding: 1rem;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  background: #f9f9f9;
}

.trackingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.carrier {
  font-weight: 600;
  color: #1a1a1a;
}

.trackingNumber {
  font-family: monospace;
  color: #666;
  font-size: 0.875rem;
}

.trackingLink {
  color: #5a31f4;
  text-decoration: none;
  font-weight: 500;
}

.trackingLink:hover {
  text-decoration: underline;
}

.lineItemsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.lineItem {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
}

.itemImage {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  background: #f5f5f5;
}

.itemDetails {
  flex: 1;
}

.itemTitle {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.itemQuantity {
  color: #666;
  font-size: 0.875rem;
}

.actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e5e5;
}

.button {
  flex: 1;
  padding: 0.875rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  text-decoration: none;
  border: none;
}

.buttonPrimary {
  background: #5a31f4;
  color: white;
}

.buttonPrimary:hover {
  background: #4c28d4;
}

.buttonSecondary {
  background: #f5f5f5;
  color: #1a1a1a;
  border: 1px solid #e5e5e5;
}

.buttonSecondary:hover {
  background: #e5e5e5;
}

.backButton {
  background: none;
  border: none;
  color: #5a31f4;
  font-size: 0.875rem;
  cursor: pointer;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.backButton:hover {
  text-decoration: underline;
}

@media (max-width: 640px) {
  .container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .orderInfo {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .lineItem {
    flex-direction: column;
    text-align: center;
  }
  
  .itemImage {
    align-self: center;
  }
}
