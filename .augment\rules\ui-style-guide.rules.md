# Rule: UI Style and Component Guide

**Mode**: Auto

**Purpose**: To provide guidelines for the visual design and component structure of the widget.

**Description**: Guidelines for the visual style, responsiveness, and component architecture of the frontend widget.

## Requirements

1. The overall design aesthetic should be clean, minimalist, and modern.

2. Use CSS Modules for component-level styling to prevent global scope conflicts and ensure component encapsulation. Do not use global CSS files except for base resets or font definitions.

3. The UI must be fully responsive and provide a seamless experience on both desktop and mobile viewports. Use media queries to adapt the layout.

4. All interactive elements (buttons, links) MUST have clear hover and focus states to ensure good usability and accessibility.

5. The primary brand color for interactive elements is #5a31f4. Text color is #1a1a1a and background is #ffffff.

6. Use a consistent font, such as Inter, across the entire widget.
