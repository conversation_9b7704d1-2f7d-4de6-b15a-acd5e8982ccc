import '@shopify/shopify-api/adapters/node';
declare const shopify: import("@shopify/shopify-api").Shopify<{
    apiKey: string;
    apiSecretKey: string;
    scopes: string[];
    hostName: string;
    apiVersion: import("@shopify/shopify-api").ApiVersion.April24;
    isEmbeddedApp: false;
}, import("@shopify/shopify-api").ShopifyRestResources, import("@shopify/shopify-api/dist/ts/future/flags").FutureFlagOptions>;
export declare const createGraphQLClient: () => import("@shopify/shopify-api").GraphqlClient;
export { shopify };
//# sourceMappingURL=shopify-client.d.ts.map