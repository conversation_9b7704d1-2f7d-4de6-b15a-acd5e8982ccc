import { OrderLookupForm, OrderLookupResponse, ReturnRequest, ReturnCreateResponse, ReturnLabelResponse } from '../types/api';

const API_BASE_URL = '/api';

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function fetchApi<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new ApiError(response.status, errorData.error || `HTTP ${response.status}`);
  }

  return response.json();
}

export const apiClient = {
  // Find order by order number and email
  async findOrder(orderLookup: OrderLookupForm): Promise<OrderLookupResponse> {
    try {
      return await fetchApi<OrderLookupResponse>('/orders/find', {
        method: 'POST',
        body: JSON.stringify(orderLookup),
      });
    } catch (error) {
      if (error instanceof ApiError) {
        return {
          success: false,
          error: error.message,
        };
      }
      return {
        success: false,
        error: 'Failed to find order. Please try again.',
      };
    }
  },

  // Create a return
  async createReturn(returnRequest: ReturnRequest): Promise<ReturnCreateResponse> {
    try {
      return await fetchApi<ReturnCreateResponse>('/returns/create', {
        method: 'POST',
        body: JSON.stringify(returnRequest),
      });
    } catch (error) {
      if (error instanceof ApiError) {
        return {
          success: false,
          error: error.message,
        };
      }
      return {
        success: false,
        error: 'Failed to create return. Please try again.',
      };
    }
  },

  // Get return shipping label
  async getReturnLabel(returnId: string): Promise<ReturnLabelResponse> {
    try {
      return await fetchApi<ReturnLabelResponse>(`/returns/${returnId}/label`);
    } catch (error) {
      if (error instanceof ApiError) {
        return {
          success: false,
          error: error.message,
        };
      }
      return {
        success: false,
        error: 'Failed to generate return label. Please try again.',
      };
    }
  },
};
