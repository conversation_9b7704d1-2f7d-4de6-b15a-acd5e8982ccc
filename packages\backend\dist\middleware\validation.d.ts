import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
export declare const OrderLookupSchema: z.ZodObject<{
    orderNumber: z.ZodString;
    email: z.ZodString;
}, "strip", z.ZodTypeAny, {
    orderNumber: string;
    email: string;
}, {
    orderNumber: string;
    email: string;
}>;
export declare const ReturnLineItemSchema: z.ZodObject<{
    fulfillmentLineItemId: z.ZodString;
    quantity: z.ZodNumber;
    returnReason: z.ZodString;
    returnReasonNote: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    fulfillmentLineItemId: string;
    quantity: number;
    returnReason: string;
    returnReasonNote?: string | undefined;
}, {
    fulfillmentLineItemId: string;
    quantity: number;
    returnReason: string;
    returnReasonNote?: string | undefined;
}>;
export declare const ReturnCreateSchema: z.ZodObject<{
    orderId: z.ZodString;
    returnLineItems: z.Zod<PERSON>rray<z.ZodObject<{
        fulfillmentLineItemId: z.ZodString;
        quantity: z.ZodNumber;
        returnReason: z.ZodString;
        returnReasonNote: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        fulfillmentLineItemId: string;
        quantity: number;
        returnReason: string;
        returnReasonNote?: string | undefined;
    }, {
        fulfillmentLineItemId: string;
        quantity: number;
        returnReason: string;
        returnReasonNote?: string | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    orderId: string;
    returnLineItems: {
        fulfillmentLineItemId: string;
        quantity: number;
        returnReason: string;
        returnReasonNote?: string | undefined;
    }[];
}, {
    orderId: string;
    returnLineItems: {
        fulfillmentLineItemId: string;
        quantity: number;
        returnReason: string;
        returnReasonNote?: string | undefined;
    }[];
}>;
export declare const validateBody: (schema: z.ZodSchema) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const errorHandler: (error: Error, req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=validation.d.ts.map