# Shopify Order Tracking & Returns Widget

A secure, self-service order tracking and returns widget for Shopify stores. Built with React, TypeScript, Node.js, and the Shopify GraphQL Admin API.

## Architecture

This project follows a secure three-tier architecture:

- **Frontend Widget**: React application with TypeScript and Vite
- **Backend Service**: Node.js/Express API proxy with TypeScript
- **Shopify Platform**: GraphQL Admin API integration

## Features

- **Order Lookup**: Find orders by order number and email
- **Order Tracking**: View order status and tracking information
- **Return Initiation**: Select items to return with reasons
- **Return Labels**: Generate and download return shipping labels

## Prerequisites

- Node.js 20.10.0 or higher
- pnpm 8.0.0 or higher
- Shopify store with Admin API access

## Setup

### 1. Install Dependencies

```bash
pnpm install
```

### 2. Configure Shopify API

1. Create a Custom App in your Shopify Admin:
   - Go to Settings > Apps and sales channels > Develop apps
   - Create a new app

2. Configure API scopes in the app's Configuration tab:
   - `read_orders`
   - `write_orders`
   - `read_returns`
   - `write_returns`
   - `read_fulfillments`
   - `read_merchant_managed_fulfillment_orders`

3. Get your API credentials from the API credentials tab

### 3. Environment Configuration

Copy the example environment file and configure your credentials:

```bash
cp packages/backend/.env.example packages/backend/.env
```

Edit `packages/backend/.env`:

```env
SHOPIFY_STORE_DOMAIN=your-store.myshopify.com
SHOPIFY_ACCESS_TOKEN=your-admin-api-access-token
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
```

## Development

### Start Development Servers

Run both frontend and backend in development mode:

```bash
pnpm dev
```

This will start:
- Frontend: http://localhost:3000
- Backend: http://localhost:3001

### Individual Package Commands

**Backend only:**
```bash
cd packages/backend
pnpm dev
```

**Frontend only:**
```bash
cd packages/frontend
pnpm dev
```

## API Endpoints

### Backend API

- `POST /api/orders/find` - Find order by order number and email
- `POST /api/returns/create` - Create a return
- `GET /api/returns/:id/label` - Generate return shipping label
- `GET /health` - Health check

## Project Structure

```
├── packages/
│   ├── backend/           # Node.js/Express API service
│   │   ├── src/
│   │   │   ├── lib/       # Shopify client configuration
│   │   │   ├── services/  # Business logic
│   │   │   ├── routes/    # API endpoints
│   │   │   ├── middleware/# Validation and error handling
│   │   │   └── types/     # TypeScript type definitions
│   │   └── package.json
│   └── frontend/          # React application
│       ├── src/
│       │   ├── components/# React components
│       │   ├── lib/       # API client
│       │   └── types/     # TypeScript type definitions
│       └── package.json
├── .augment/
│   └── rules/             # Augment Code AI rules
├── widget.prd.md          # Product requirements
└── package.json           # Workspace configuration
```

## Security

- All Shopify API credentials are stored securely in the backend
- Frontend never contains sensitive credentials
- CORS protection configured
- Input validation on all API endpoints
- Error messages sanitized for frontend

## Testing

Run tests for all packages:

```bash
pnpm test
```

## Building for Production

Build all packages:

```bash
pnpm build
```

## Deployment

### Backend Deployment

Deploy the backend service to a platform like Vercel, Heroku, or Railway:

1. Set environment variables in your deployment platform
2. Deploy the `packages/backend` directory
3. Ensure the health check endpoint is accessible

### Frontend Deployment

Deploy the frontend to a static hosting service like Vercel or Netlify:

1. Build the frontend: `cd packages/frontend && pnpm build`
2. Deploy the `packages/frontend/dist` directory
3. Configure the API proxy to point to your backend service

## Troubleshooting

### Common Issues

1. **"Missing environment variables"**: Ensure all required environment variables are set in `.env`
2. **CORS errors**: Check that `FRONTEND_URL` matches your frontend URL
3. **Shopify API errors**: Verify your API scopes and access token
4. **Order not found**: Ensure the order number format matches Shopify's format (e.g., "#1001")

### Health Check

Check if the backend is running:

```bash
curl http://localhost:3001/health
```

## Contributing

1. Follow the existing code style and patterns
2. Add tests for new features
3. Update documentation as needed
4. Follow the Augment Code rules in `.augment/rules/`
