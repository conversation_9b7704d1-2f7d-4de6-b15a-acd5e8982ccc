{"name": "@shopify-widget/backend", "version": "1.0.0", "description": "Backend API service for Shopify order tracking and returns widget", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"@shopify/shopify-api": "^10.0.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}