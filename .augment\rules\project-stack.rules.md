# Rule: Project Technology Stack and Structure

**Mode**: Always

**Purpose**: To define the core technology stack and project structure.

## Requirements

1. This project is a monorepo containing two packages: frontend and backend.

2. The frontend package MUST be a React application built with TypeScript and Vite.

3. Frontend components MUST be functional components using React Hooks. Class components are not permitted.

4. The backend package MUST be a Node.js application using the Express framework and TypeScript.

5. Use pnpm as the package manager for the workspace.

6. Adhere to a clean separation of concerns. API logic, business logic, and data access should be in distinct modules within the backend.
