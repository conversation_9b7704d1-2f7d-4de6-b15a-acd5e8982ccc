import { shopifyApi, LATEST_API_VERSION, Session } from '@shopify/shopify-api';
import '@shopify/shopify-api/adapters/node';

// Initialize Shopify API
const shopify = shopifyApi({
  apiKey: process.env.SHOPIFY_API_KEY || 'not-needed-for-custom-app',
  apiSecretKey: process.env.SHOPIFY_API_SECRET || 'not-needed-for-custom-app',
  scopes: ['read_orders', 'write_orders', 'read_returns', 'write_returns', 'read_fulfillments', 'read_merchant_managed_fulfillment_orders'],
  hostName: process.env.HOST || 'localhost',
  apiVersion: LATEST_API_VERSION,
  isEmbeddedApp: false,
});

// Create a session for custom app
const createSession = (): Session => {
  const storeDomain = process.env.SHOPIFY_STORE_DOMAIN;
  const accessToken = process.env.SHOPIFY_ACCESS_TOKEN;

  if (!storeDomain || !accessToken) {
    throw new Error('Missing required Shopify configuration: SHOPIFY_STORE_DOMAIN and SHOPIFY_ACCESS_TOKEN must be set');
  }

  return shopify.session.customAppSession(storeDomain);
};

// Create GraphQL client
export const createGraphQLClient = () => {
  const session = createSession();
  session.accessToken = process.env.SHOPIFY_ACCESS_TOKEN!;
  
  return new shopify.clients.Graphql({ session });
};

export { shopify };
