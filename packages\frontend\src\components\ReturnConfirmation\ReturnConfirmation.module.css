.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.successIcon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: #dcfce7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #166534;
  font-size: 2rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.returnInfo {
  background: #f9f9f9;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e5e5;
}

.infoItem:last-child {
  border-bottom: none;
}

.infoLabel {
  font-weight: 500;
  color: #666;
}

.infoValue {
  font-weight: 600;
  color: #1a1a1a;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.button {
  padding: 0.875rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  text-decoration: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.buttonPrimary {
  background: #5a31f4;
  color: white;
}

.buttonPrimary:hover {
  background: #4c28d4;
}

.buttonPrimary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.buttonSecondary {
  background: #f5f5f5;
  color: #1a1a1a;
  border: 1px solid #e5e5e5;
}

.buttonSecondary:hover {
  background: #e5e5e5;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  text-align: left;
}

.errorTitle {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.fallbackInstructions {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  text-align: left;
}

.fallbackTitle {
  font-weight: 600;
  color: #92400e;
  margin-bottom: 0.5rem;
}

.fallbackText {
  color: #92400e;
  font-size: 0.875rem;
  line-height: 1.5;
}

.backButton {
  background: none;
  border: none;
  color: #5a31f4;
  font-size: 0.875rem;
  cursor: pointer;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.backButton:hover {
  text-decoration: underline;
}

@media (max-width: 640px) {
  .container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .title {
    font-size: 1.25rem;
  }
  
  .returnInfo {
    padding: 1rem;
  }
  
  .infoItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
