# Rule: Robust Error Handling and User Feedback

**Mode**: Auto

**Purpose**: To guide the AI on implementing robust error handling.

**Description**: Rules for implementing comprehensive error handling and user-friendly feedback mechanisms.

## Requirements

1. When a backend API call to Shopify fails, the detailed error (including the API response) MUST be logged to the server console for debugging purposes.

2. The backend MUST NOT expose raw Shopify error messages to the frontend. Instead, it should map API errors to a set of standardized, user-friendly error messages.

3. The backend should return appropriate HTTP status codes: 200 for success, 400 for bad client requests (e.g., invalid input), 404 for resources not found (e.g., order lookup failure), and 500 for internal server errors.

4. The frontend MUST gracefully handle API errors. It should display the user-friendly message received from the backend in a non-intrusive UI element (e.g., a toast notification or an inline message).

5. Input fields on the frontend should have client-side validation (e.g., ensuring the email field contains a valid email format) before submission.
