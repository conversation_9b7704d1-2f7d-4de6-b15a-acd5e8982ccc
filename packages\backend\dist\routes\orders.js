"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const order_service_js_1 = require("../services/order-service.js");
const validation_js_1 = require("../middleware/validation.js");
const router = (0, express_1.Router)();
const orderService = new order_service_js_1.OrderService();
// POST /api/orders/find - Find order by order number and email
router.post('/find', (0, validation_js_1.validateBody)(validation_js_1.OrderLookupSchema), async (req, res, next) => {
    try {
        const { orderNumber, email } = req.body;
        console.log(`Order lookup request: ${orderNumber} for ${email}`);
        const result = await orderService.findOrder(orderNumber, email);
        if (!result.order) {
            return res.status(404).json({
                error: 'Order not found. Please check your order number and email and try again.',
            });
        }
        res.json({
            success: true,
            order: result.order,
        });
    }
    catch (error) {
        next(error);
    }
});
exports.default = router;
//# sourceMappingURL=orders.js.map