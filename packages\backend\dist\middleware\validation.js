"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.validateBody = exports.ReturnCreateSchema = exports.ReturnLineItemSchema = exports.OrderLookupSchema = void 0;
const zod_1 = require("zod");
// Validation schemas
exports.OrderLookupSchema = zod_1.z.object({
    orderNumber: zod_1.z.string().min(1, 'Order number is required'),
    email: zod_1.z.string().email('Valid email is required'),
});
exports.ReturnLineItemSchema = zod_1.z.object({
    fulfillmentLineItemId: zod_1.z.string().min(1, 'Fulfillment line item ID is required'),
    quantity: zod_1.z.number().int().min(1, 'Quantity must be at least 1'),
    returnReason: zod_1.z.string().min(1, 'Return reason is required'),
    returnReasonNote: zod_1.z.string().optional(),
});
exports.ReturnCreateSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1, 'Order ID is required'),
    returnLineItems: zod_1.z.array(exports.ReturnLineItemSchema).min(1, 'At least one item must be selected for return'),
});
// Validation middleware factory
const validateBody = (schema) => {
    return (req, res, next) => {
        try {
            const validatedData = schema.parse(req.body);
            req.body = validatedData;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const errorMessages = error.errors.map(err => ({
                    field: err.path.join('.'),
                    message: err.message,
                }));
                return res.status(400).json({
                    error: 'Validation failed',
                    details: errorMessages,
                });
            }
            return res.status(400).json({
                error: 'Invalid request data',
            });
        }
    };
};
exports.validateBody = validateBody;
// Error handling middleware
const errorHandler = (error, req, res, next) => {
    console.error('API Error:', error);
    // Map specific errors to user-friendly messages
    let statusCode = 500;
    let message = 'An internal server error occurred';
    if (error.message.includes('Order not found') || error.message.includes('No order found')) {
        statusCode = 404;
        message = 'Order not found. Please check your order number and email and try again.';
    }
    else if (error.message.includes('Return creation failed')) {
        statusCode = 400;
        message = 'Unable to create return. Please ensure the items are eligible for return.';
    }
    else if (error.message.includes('Failed to find order')) {
        statusCode = 500;
        message = 'Unable to search for orders at this time. Please try again later.';
    }
    else if (error.message.includes('Failed to create return')) {
        statusCode = 500;
        message = 'Unable to process return request at this time. Please try again later.';
    }
    res.status(statusCode).json({
        error: message,
    });
};
exports.errorHandler = errorHandler;
//# sourceMappingURL=validation.js.map