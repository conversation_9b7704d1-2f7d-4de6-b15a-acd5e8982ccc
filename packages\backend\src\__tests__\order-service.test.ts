import { OrderService } from '../services/order-service';

// Mock the Shopify client
jest.mock('../lib/shopify-client', () => ({
  createGraphQLClient: jest.fn(() => ({
    query: jest.fn(),
  })),
}));

describe('OrderService', () => {
  let orderService: OrderService;
  let mockClient: any;

  beforeEach(() => {
    const { createGraphQLClient } = require('../lib/shopify-client');
    mockClient = createGraphQLClient();
    orderService = new OrderService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findOrder', () => {
    it('should return order when found', async () => {
      const mockOrder = {
        id: 'gid://shopify/Order/123',
        name: '#1001',
        email: '<EMAIL>',
        displayFulfillmentStatus: 'FULFILLED',
        financialStatus: 'PAID',
        fulfillments: { edges: [] },
        lineItems: { edges: [] },
      };

      mockClient.query.mockResolvedValue({
        body: {
          data: {
            orders: {
              edges: [{ node: mockOrder }],
            },
          },
        },
      });

      const result = await orderService.findOrder('#1001', '<EMAIL>');

      expect(result.order).toEqual(mockOrder);
      expect(mockClient.query).toHaveBeenCalledWith({
        data: {
          query: expect.stringContaining('query findOrder'),
          variables: { query: 'name:#1001 AND email:"<EMAIL>"' },
        },
      });
    });

    it('should return null when order not found', async () => {
      mockClient.query.mockResolvedValue({
        body: {
          data: {
            orders: {
              edges: [],
            },
          },
        },
      });

      const result = await orderService.findOrder('#1001', '<EMAIL>');

      expect(result.order).toBeNull();
    });

    it('should throw error when API call fails', async () => {
      mockClient.query.mockRejectedValue(new Error('API Error'));

      await expect(
        orderService.findOrder('#1001', '<EMAIL>')
      ).rejects.toThrow('Failed to find order');
    });
  });
});
