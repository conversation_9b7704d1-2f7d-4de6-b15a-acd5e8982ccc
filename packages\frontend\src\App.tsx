import React, { useState } from 'react';
import { OrderLookup } from './components/OrderLookup/OrderLookup';
import { OrderDetails } from './components/OrderDetails/OrderDetails';
import { ReturnSelection } from './components/ReturnSelection/ReturnSelection';
import { ReturnConfirmation } from './components/ReturnConfirmation/ReturnConfirmation';
import { Order } from './types/api';
import './App.css';

type AppState = 'lookup' | 'order-details' | 'return-selection' | 'return-confirmation';

interface AppData {
  order: Order | null;
  returnId: string | null;
}

function App() {
  const [currentState, setCurrentState] = useState<AppState>('lookup');
  const [appData, setAppData] = useState<AppData>({
    order: null,
    returnId: null,
  });

  const handleOrderFound = (order: Order) => {
    setAppData(prev => ({ ...prev, order }));
    setCurrentState('order-details');
  };

  const handleStartReturn = () => {
    setCurrentState('return-selection');
  };

  const handleReturnCreated = (returnId: string) => {
    setAppData(prev => ({ ...prev, returnId }));
    setCurrentState('return-confirmation');
  };

  const handleBackToLookup = () => {
    setCurrentState('lookup');
    setAppData({ order: null, returnId: null });
  };

  const handleBackToOrderDetails = () => {
    setCurrentState('order-details');
  };

  const handleBackToReturnSelection = () => {
    setCurrentState('return-selection');
  };

  const renderCurrentScreen = () => {
    switch (currentState) {
      case 'lookup':
        return <OrderLookup onOrderFound={handleOrderFound} />;
      
      case 'order-details':
        if (!appData.order) {
          setCurrentState('lookup');
          return null;
        }
        return (
          <OrderDetails 
            order={appData.order}
            onStartReturn={handleStartReturn}
            onBack={handleBackToLookup}
          />
        );
      
      case 'return-selection':
        if (!appData.order) {
          setCurrentState('lookup');
          return null;
        }
        return (
          <ReturnSelection 
            order={appData.order}
            onReturnCreated={handleReturnCreated}
            onBack={handleBackToOrderDetails}
          />
        );
      
      case 'return-confirmation':
        if (!appData.order || !appData.returnId) {
          setCurrentState('lookup');
          return null;
        }
        return (
          <ReturnConfirmation 
            returnId={appData.returnId}
            orderName={appData.order.name}
            onBack={handleBackToReturnSelection}
            onStartOver={handleBackToLookup}
          />
        );
      
      default:
        return <OrderLookup onOrderFound={handleOrderFound} />;
    }
  };

  return (
    <div className="app">
      <div className="app-container">
        {renderCurrentScreen()}
      </div>
    </div>
  );
}

export default App;
