"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.shopify = exports.createGraphQLClient = void 0;
const shopify_api_1 = require("@shopify/shopify-api");
require("@shopify/shopify-api/adapters/node");
// Initialize Shopify API
const shopify = (0, shopify_api_1.shopifyApi)({
    apiKey: process.env.SHOPIFY_API_KEY || 'not-needed-for-custom-app',
    apiSecretKey: process.env.SHOPIFY_API_SECRET || 'not-needed-for-custom-app',
    scopes: ['read_orders', 'write_orders', 'read_returns', 'write_returns', 'read_fulfillments', 'read_merchant_managed_fulfillment_orders'],
    hostName: process.env.HOST || 'localhost',
    apiVersion: shopify_api_1.LATEST_API_VERSION,
    isEmbeddedApp: false,
});
exports.shopify = shopify;
// Create a session for custom app
const createSession = () => {
    const storeDomain = process.env.SHOPIFY_STORE_DOMAIN;
    const accessToken = process.env.SHOPIFY_ACCESS_TOKEN;
    if (!storeDomain || !accessToken) {
        throw new Error('Missing required Shopify configuration: SHOPIFY_STORE_DOMAIN and SHOPIFY_ACCESS_TOKEN must be set');
    }
    return shopify.session.customAppSession(storeDomain);
};
// Create GraphQL client
const createGraphQLClient = () => {
    const session = createSession();
    session.accessToken = process.env.SHOPIFY_ACCESS_TOKEN;
    return new shopify.clients.Graphql({ session });
};
exports.createGraphQLClient = createGraphQLClient;
//# sourceMappingURL=shopify-client.js.map