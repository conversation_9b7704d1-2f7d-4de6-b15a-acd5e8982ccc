import React from 'react';
import { Order, TrackingInfo } from '../../types/api';
import styles from './OrderDetails.module.css';

interface OrderDetailsProps {
  order: Order;
  onStartReturn: () => void;
  onBack: () => void;
}

export const OrderDetails: React.FC<OrderDetailsProps> = ({ order, onStartReturn, onBack }) => {
  const getStatusClass = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    if (normalizedStatus.includes('delivered')) return 'delivered';
    if (normalizedStatus.includes('shipped') || normalizedStatus.includes('transit')) return 'shipped';
    return 'pending';
  };

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getAllTrackingInfo = (): TrackingInfo[] => {
    const trackingInfo: TrackingInfo[] = [];
    
    order.fulfillments.edges.forEach(fulfillment => {
      trackingInfo.push(...fulfillment.node.trackingInfo);
    });
    
    return trackingInfo;
  };

  const trackingInfo = getAllTrackingInfo();
  const hasReturnableItems = order.lineItems.edges.some(
    item => item.node.fulfillableQuantity > 0
  );

  return (
    <div className={styles.container}>
      <button className={styles.backButton} onClick={onBack}>
        ← Back to Search
      </button>

      <div className={styles.header}>
        <h1 className={styles.title}>Order {order.name}</h1>
        
        <div className={styles.orderInfo}>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>Order Status</span>
            <span className={`${styles.status} ${styles[getStatusClass(order.displayFulfillmentStatus)]}`}>
              {formatStatus(order.displayFulfillmentStatus)}
            </span>
          </div>
          
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>Payment Status</span>
            <span className={styles.infoValue}>
              {formatStatus(order.financialStatus)}
            </span>
          </div>
          
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>Email</span>
            <span className={styles.infoValue}>{order.email}</span>
          </div>
        </div>
      </div>

      {trackingInfo.length > 0 && (
        <div className={styles.section}>
          <h2 className={styles.sectionTitle}>Tracking Information</h2>
          <div className={styles.trackingList}>
            {trackingInfo.map((tracking, index) => (
              <div key={index} className={styles.trackingItem}>
                <div className={styles.trackingHeader}>
                  <span className={styles.carrier}>{tracking.company}</span>
                  <span className={styles.trackingNumber}>{tracking.number}</span>
                </div>
                {tracking.url && (
                  <a 
                    href={tracking.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className={styles.trackingLink}
                  >
                    Track Package →
                  </a>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className={styles.section}>
        <h2 className={styles.sectionTitle}>Order Items</h2>
        <div className={styles.lineItemsList}>
          {order.lineItems.edges.map(({ node: item }) => (
            <div key={item.id} className={styles.lineItem}>
              {item.image && (
                <img 
                  src={item.image.url} 
                  alt={item.title}
                  className={styles.itemImage}
                />
              )}
              <div className={styles.itemDetails}>
                <div className={styles.itemTitle}>{item.title}</div>
                <div className={styles.itemQuantity}>
                  Quantity: {item.quantity}
                  {item.fulfillableQuantity > 0 && (
                    <span> • {item.fulfillableQuantity} available for return</span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className={styles.actions}>
        {trackingInfo.length > 0 && (
          <button className={`${styles.button} ${styles.buttonSecondary}`}>
            View Tracking Details
          </button>
        )}
        
        {hasReturnableItems && (
          <button 
            className={`${styles.button} ${styles.buttonPrimary}`}
            onClick={onStartReturn}
          >
            Start a Return
          </button>
        )}
      </div>
    </div>
  );
};
