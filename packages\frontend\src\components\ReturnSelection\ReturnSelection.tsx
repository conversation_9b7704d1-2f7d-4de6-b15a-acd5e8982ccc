import React, { useState } from 'react';
import { Order, ReturnSelection as ReturnSelectionType, RETURN_REASONS, ReturnRequest } from '../../types/api';
import { apiClient } from '../../lib/api-client';
import styles from './ReturnSelection.module.css';

interface ReturnSelectionProps {
  order: Order;
  onReturnCreated: (returnId: string) => void;
  onBack: () => void;
}

export const ReturnSelection: React.FC<ReturnSelectionProps> = ({ order, onReturnCreated, onBack }) => {
  const [selection, setSelection] = useState<ReturnSelectionType>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const returnableItems = order.lineItems.edges.filter(
    item => item.node.fulfillableQuantity > 0
  );

  const handleItemToggle = (itemId: string, checked: boolean) => {
    setSelection(prev => ({
      ...prev,
      [itemId]: checked 
        ? {
            selected: true,
            quantity: 1,
            returnReason: RETURN_REASONS[0],
          }
        : {
            selected: false,
            quantity: 0,
            returnReason: RETURN_REASONS[0],
          }
    }));
    
    if (error) setError('');
  };

  const handleQuantityChange = (itemId: string, quantity: number) => {
    const item = returnableItems.find(item => item.node.id === itemId);
    const maxQuantity = item?.node.fulfillableQuantity || 0;
    
    const validQuantity = Math.max(1, Math.min(quantity, maxQuantity));
    
    setSelection(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        quantity: validQuantity,
      }
    }));
  };

  const handleReasonChange = (itemId: string, reason: string) => {
    setSelection(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        returnReason: reason,
      }
    }));
  };

  const handleReasonNoteChange = (itemId: string, note: string) => {
    setSelection(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        returnReasonNote: note,
      }
    }));
  };

  const getSelectedItems = () => {
    return Object.entries(selection).filter(([_, item]) => item.selected);
  };

  const hasSelectedItems = getSelectedItems().length > 0;

  const handleSubmit = async () => {
    const selectedItems = getSelectedItems();
    
    if (selectedItems.length === 0) {
      setError('Please select at least one item to return.');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const returnLineItems = selectedItems.map(([itemId, item]) => ({
        fulfillmentLineItemId: itemId,
        quantity: item.quantity,
        returnReason: item.returnReason,
        returnReasonNote: item.returnReasonNote,
      }));

      const returnRequest: ReturnRequest = {
        orderId: order.id,
        returnLineItems,
      };

      const response = await apiClient.createReturn(returnRequest);
      
      if (response.success && response.returnId) {
        onReturnCreated(response.returnId);
      } else {
        setError(response.error || 'Failed to create return. Please try again.');
      }
    } catch (error) {
      setError('Unable to process return request at this time. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <button className={styles.backButton} onClick={onBack}>
        ← Back to Order Details
      </button>

      <div className={styles.header}>
        <h1 className={styles.title}>Select Items to Return</h1>
        <p className={styles.subtitle}>
          Choose the items you'd like to return from order {order.name}
        </p>
      </div>

      <div className={styles.itemsList}>
        {returnableItems.map(({ node: item }) => {
          const isSelected = selection[item.id]?.selected || false;
          const selectedQuantity = selection[item.id]?.quantity || 1;
          const selectedReason = selection[item.id]?.returnReason || RETURN_REASONS[0];
          const selectedNote = selection[item.id]?.returnReasonNote || '';

          return (
            <div
              key={item.id}
              className={`${styles.itemCard} ${isSelected ? styles.selected : ''}`}
            >
              <div className={styles.itemHeader}>
                {item.image && (
                  <img
                    src={item.image.url}
                    alt={item.title}
                    className={styles.itemImage}
                  />
                )}
                <div className={styles.itemInfo}>
                  <div className={styles.itemTitle}>{item.title}</div>
                  <div className={styles.itemQuantity}>
                    {item.fulfillableQuantity} available for return
                  </div>
                </div>
              </div>

              <div className={styles.selectionControls}>
                <label>
                  <input
                    type="checkbox"
                    className={styles.checkbox}
                    checked={isSelected}
                    onChange={(e) => handleItemToggle(item.id, e.target.checked)}
                  />
                  Return this item
                </label>

                {isSelected && (
                  <div className={styles.quantityControl}>
                    <span className={styles.quantityLabel}>Quantity:</span>
                    <input
                      type="number"
                      min="1"
                      max={item.fulfillableQuantity}
                      value={selectedQuantity}
                      onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value) || 1)}
                      className={styles.quantityInput}
                    />
                  </div>
                )}
              </div>

              {isSelected && (
                <div className={styles.reasonSection}>
                  <label className={styles.reasonLabel}>
                    Reason for return:
                  </label>
                  <select
                    value={selectedReason}
                    onChange={(e) => handleReasonChange(item.id, e.target.value)}
                    className={styles.reasonSelect}
                  >
                    {RETURN_REASONS.map(reason => (
                      <option key={reason} value={reason}>
                        {reason}
                      </option>
                    ))}
                  </select>

                  <textarea
                    placeholder="Additional details (optional)"
                    value={selectedNote}
                    onChange={(e) => handleReasonNoteChange(item.id, e.target.value)}
                    className={styles.reasonNote}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {error && (
        <div className={styles.error}>{error}</div>
      )}

      <div className={styles.actions}>
        <button
          className={`${styles.button} ${styles.buttonSecondary}`}
          onClick={onBack}
          disabled={isLoading}
        >
          Cancel
        </button>

        <button
          className={`${styles.button} ${styles.buttonPrimary}`}
          onClick={handleSubmit}
          disabled={!hasSelectedItems || isLoading}
        >
          {isLoading ? (
            <span className={styles.loading}>
              <span className={styles.spinner}></span>
              Creating Return...
            </span>
          ) : (
            'Continue'
          )}
        </button>
      </div>
    </div>
  );
};
